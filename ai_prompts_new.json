{"version": "1.0", "last_updated": "2025-08-11", "prompts": {"test_prompt": {"name": "Test Prompt", "description": "Prompt for test prompt", "prompt": "This is a test prompt for genealogical analysis"}, "intent_classification": {"name": "Intent Classification", "description": "Prompt for classifying user intent in genealogy messages", "prompt": "You are an AI assistant analyzing conversation histories from a genealogy website messaging system. The history alternates between 'SCRIPT' (automated messages from me) and 'USER' (replies from the DNA match).\n\nAnalyze the entire provided conversation history, interpreting the **last message sent by the USER** *within the context of the entire conversation history* provided below. Determine the primary intent of that final USER message.\n\nRespond ONLY with one of the following single-word categories:\n- ENTHUSIASTIC: User is actively engaging with genealogy research, sharing detailed family information, asking specific genealogical questions, expressing excitement, or offering to share documents/photos.\n- CAUTIOUSLY_INTERESTED: User shows measured interest, requesting more information before committing, expressing uncertainty, or asking for verification.\n- UNINTERESTED: User politely declines further contact, states they cannot help, don't have time, are not knowledgeable, shows clear lack of engagement/desire to continue, or replies with very short, non-committal answers that don't advance the genealogical discussion after specific requests for information.\n- CONFUSED: User doesn't understand the context, is unclear why they received the message, or doesn't understand DNA matching/genealogy concepts.\n- PRODUCTIVE: User's final message, in context, provides helpful genealogical information (names, dates, places, relationships), asks relevant clarifying questions, confirms relationships, expresses clear interest in collaborating, or shares tree info/invites.\n- DESIST: The user's final message, in context, explicitly asks to stop receiving messages or indicates they are blocking the sender.\n- OTHER: Messages that don't clearly fit above categories (e.g., purely social pleasantries, unrelated questions, ambiguous statements, or messages containing only attachments/links without explanatory text).\n\nCRITICAL: Your entire response must be only one of the category words."}, "extraction_task": {"name": "Enhanced Genealogical Data Extraction & Task Suggestion", "description": "Structured extraction prompt aligned with ExtractedData Pydantic model", "prompt_version": "1.1.0", "prompt": "You are an expert genealogy research assistant. Extract ONLY explicitly stated genealogical facts from the conversation and produce STRICT JSON with two top-level keys: extracted_data and suggested_tasks. Do not add narrative text outside JSON.\n\nRULES:\n- No fabrication or inference beyond explicit statements.\n- Use empty arrays [] for categories with no data.\n- Dates: preserve original form; if approximate use provided qualifier (e.g. 'circa 1850', '~1850').\n- Certainty: classify each vital_records entry as certain / probable / uncertain (default 'certain' only if directly stated).\n- Normalize spacing, keep original capitalization of names.\n- suggested_tasks: 3-8 concise, actionable research tasks (verbs first), no duplicates, each under 140 chars.\n\nOUTPUT SCHEMA (exact keys):\n{\n  \"extracted_data\": {\n    \"structured_names\": [{\n      \"full_name\": \"...\", \"nicknames\": [\"...\"], \"maiden_name\": null, \"generational_suffix\": null\n    }],\n    \"vital_records\": [{\n      \"person\": \"...\", \"event_type\": \"birth|death|marriage|baptism|burial\", \"date\": \"...\", \"place\": \"...\", \"certainty\": \"certain|probable|uncertain\"\n    }],\n    \"relationships\": [{\n      \"person1\": \"...\", \"relationship\": \"father|mother|spouse|child|sibling|other\", \"person2\": \"...\", \"context\": \"...\"\n    }],\n    \"locations\": [{\n      \"place\": \"...\", \"context\": \"residence|birthplace|workplace|...\", \"time_period\": \"...\"\n    }],\n    \"occupations\": [{\n      \"person\": \"...\", \"occupation\": \"...\", \"location\": \"...\", \"time_period\": \"...\"\n    }],\n    \"research_questions\": [\"...\"],\n    \"documents_mentioned\": [\"...\"],\n    \"dna_information\": [\"...\"]\n  },\n  \"suggested_tasks\": [\"...\"]\n}\n\nSAMPLE INPUT SNIPPET:\n\"My great-grandfather Charles Fetch was born in Banff, Banffshire, Scotland in 1881. He married Mary MacDonald in 1908 and they had six children. He worked as a fisherman. I'm trying to find his parents.\"\n\nSAMPLE OUTPUT (abbreviated):\n{\n  \"extracted_data\": {\n    \"structured_names\": [{\"full_name\": \"Charles Fetch\", \"nicknames\": [], \"maiden_name\": null, \"generational_suffix\": null}, {\"full_name\": \"Mary MacDonald\", \"nicknames\": [], \"maiden_name\": \"MacDonald\", \"generational_suffix\": null}],\n    \"vital_records\": [{\"person\": \"Charles Fetch\", \"event_type\": \"birth\", \"date\": \"1881\", \"place\": \"Banff, Banffshire, Scotland\", \"certainty\": \"certain\"}, {\"person\": \"Charles Fetch\", \"event_type\": \"marriage\", \"date\": \"1908\", \"place\": \"\", \"certainty\": \"certain\"}],\n    \"relationships\": [{\"person1\": \"Charles Fetch\", \"relationship\": \"spouse\", \"person2\": \"Mary MacDonald\", \"context\": \"married 1908\"}],\n    \"locations\": [{\"place\": \"Banff, Banffshire, Scotland\", \"context\": \"birthplace\", \"time_period\": \"1881\"}],\n    \"occupations\": [{\"person\": \"Charles Fetch\", \"occupation\": \"fisherman\", \"location\": \"\", \"time_period\": \"\"}],\n    \"research_questions\": [\"Identify parents of Charles Fetch\"],\n    \"documents_mentioned\": [],\n    \"dna_information\": []\n  },\n  \"suggested_tasks\": [\"Search Scottish birth record for Charles Fetch 1881 Banff\", \"Locate 1908 marriage record Charles Fetch & Mary MacDonald\", \"Check 1891 Scotland census for Fetch household in Banff\", \"Review parish registers Banff for Fetch baptisms\"]\n}\n\nReturn ONLY the JSON object."}, "genealogical_reply": {"name": "Enhanced Genealogical Reply Generation", "description": "Personalized genealogical response prompt with real examples and data integration", "prompt_version": "1.1.0", "prompt": "You are an expert genealogy assistant. Craft a personalized reply that (a) acknowledges the user's message, (b) uses ONLY provided genealogical data, (c) advances research with specific next steps, (d) asks 1–2 targeted follow‑up questions.\n\nINPUT SECTIONS:\nCONVERSATION CONTEXT:\n{conversation_context}\nUSER'S LAST MESSAGE:\n{user_message}\nSTRUCTURED GENEALOGICAL DATA (JSON):\n{genealogical_data}\n\nRESPONSE RULES:\n1. Accuracy: No speculation beyond supplied data; clearly label any uncertainty already indicated.\n2. Integration: Use names with years in parentheses if both birth & death years known: <PERSON> (1850–1920). If only one year, show single year.\n3. Relationships: Use precise terms (\"great-grandmother\", \"maternal line\").\n4. Clarity: One primary idea per paragraph; group related individuals.\n5. Actionability: Provide 2–4 concrete next research steps (records to check, verification actions).\n6. Questions: End with 1–2 specific questions that help progress (e.g., requesting document possession, clarifying a missing date).\n7. Tone: Warm, collaborative, concise; 180–320 words.\n8. Formatting: No bullet lists unless listing tasks; keep tasks concise with leading verbs.\n\nOUTPUT STRUCTURE (plain text, no JSON):\nParagraph 1: Warm acknowledgement + concise summary.\nParagraph(s) 2–3: Key findings with specifics (names, dates, places, relationships).\nParagraph 4: Actionable research steps (inline or short bullet list).\nFinal Paragraph: Follow-up questions + encouraging closing.\n\nMICRO EXAMPLE (abridged):\nHello! Thank you for the details about your great-great-grandfather Charles Fetch (1881–1948) and his marriage to Mary MacDonald in 1908. ... (continues with data-driven narrative, steps, and 2 follow-up questions).\n\nProduce only the reply text."}}}